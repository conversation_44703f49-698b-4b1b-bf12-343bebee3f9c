<?php
include "config.php";
require_once("lib/EpayCore.class.php");
require_once("mobile_pay_handler.php");

// 获取客户端IP
function get_client_ip() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

// 生成订单号
function generate_trade_no() {
    $strtotime = time();
    $year = date('Y', $strtotime);
    $month = date('m', $strtotime);
    $day = date('d', $strtotime);
    $hour = date('H', $strtotime);
    $minute = date('i', $strtotime);
    $second = date('s', $strtotime);
    $microtime = substr(microtime(), 2, 5);
    $random = rand(1000, 9999);
    
    return 'Pay' . $year . $month . $day . $hour . $minute . $second . $microtime . $random;
}

$strtotime = strtotime("now");
$date = date('Y-m-d',$strtotime);
$time = date('H:i:s',$strtotime);
$ip = get_client_ip();
$city = '未知';

// 自动检测协议和端口
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST']; // 这里已经包含了端口信息

//异步回调
$notify_url = $protocol."://".$host."/pay/notify_url.php";
//同步回调
$return_url = $protocol."://".$host."/pay/return_url.php";
//商户订单号
$out_trade_no = generate_trade_no();
$type = $post['type'];

$account = $post['account'];
$info = base64_encode('?server='.urlencode($post['server']).'&role='.urlencode($post['role']).'&roleid='.$post['roleid'].'&account='.$post['account']);

// 使用 PdoHelper 的 query 方法
$accountData = $DBDL->getRow("SELECT * FROM `account` WHERE `username` = ?", [$account]);

// 验证商品ID合法性
if(!isset($goods_info[$post['goods']]) || !is_numeric($post['goods'])) {
    exit('非法商品');
}

$money = $goods_info[$post['goods']][2];
$xianyu = $goods_info[$post['goods']][3];
$name = $account.'-'.$post['goods'].'-'.$post['roleid'];

// 验证金额合法性
if(!is_numeric($money) || $money <= 0 || $money > 10000) {
    exit('非法金额');
}

// 检查订单是否存在
$existOrder = $DBDL->getRow("SELECT id FROM pay_order WHERE orderid = ?", [$out_trade_no]);
if($existOrder) {
    exit('订单号重复');
}

//构建代理系统订单
$agentid = $accountData['agentid'] ?? 1;
if ($agentid == '') {
    $agentid = 1;
}
$checkAgent = $DBDL->query("SELECT * FROM `admin` WHERE `id` = '$agentid' ")->fetch();
if(!$checkAgent){
    $agentid = 1;
    $checkAgent = $DBDL->query("SELECT * FROM `admin` WHERE `id` = '1' ")->fetch();
}

$agents = explode(';',$checkAgent['lastuid']);
$agent = '['.$checkAgent['id'].'];'.$agents[1];

// 使用 PdoHelper 的 exec 方法插入订单
$DBDL->exec("INSERT INTO `pay_order` (`orderid`,`ordertype`,`value`,`user`,`roleid`,`rolename`,`qu`,`agent`,`money`,`status`,`ip`,`city`,`date`,`time`,`param`)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
    [
        $out_trade_no,
        '1',
        '00',
        $account,
        $post['roleid'],
        $post['role'],
        $post['server'],
        $agent,
        $money,
        '0',
        $ip,
        $city,
        $date,
        $time,
        $name
    ]
);

// 检测设备类型
function getDeviceType() {
    $userAgent = strtolower($_SERVER['HTTP_USER_AGENT']);

    if (strpos($userAgent, 'micromessenger') !== false) {
        return 'wechat';  // 微信内浏览器
    } elseif (strpos($userAgent, 'qq/') !== false) {
        return 'qq';      // 手机QQ内浏览器
    } elseif (strpos($userAgent, 'alipayclient') !== false) {
        return 'alipay';  // 支付宝客户端
    } elseif (preg_match('/(mobile|android|iphone|ipad|ipod)/i', $userAgent)) {
        return 'mobile';  // 手机浏览器
    } else {
        return 'pc';      // 电脑浏览器
    }
}

$device = getDeviceType();

// 根据设备类型选择支付方式
if ($device === 'pc') {
    // PC端使用页面跳转支付
    $parameter = array(
        "pid" => $epay_config['pid'],
        "type" => $type,
        "notify_url" => $notify_url,
        "return_url" => $return_url,
        "out_trade_no" => $out_trade_no,
        "name" => $name,
        "money" => $money,
    );

    // 计算签名
    ksort($parameter);
    $signstr = '';
    foreach($parameter as $k => $v){
        if($k != "sign" && $k != "sign_type" && $v!=''){
            $signstr .= $k.'='.$v.'&';
        }
    }
    $signstr = substr($signstr,0,-1);
    $signstr .= $epay_config['key'];
    $sign = md5($signstr);

    $parameter['sign'] = $sign;
    $parameter['sign_type'] = 'MD5';

    // 构建支付URL
    $payUrl = $epay_config['apiurl'].'submit.php?'.http_build_query($parameter);

    // 订单信息
    $orderInfo = array(
        'out_trade_no' => $out_trade_no,
        'money' => $money,
        'name' => $name,
        'type' => $type
    );

    // 使用移动端支付处理器生成页面
    echo generateMobilePayPage($payUrl, $type, $orderInfo);

} else {
    // 移动端使用API接口支付
    $parameter = array(
        "pid" => $epay_config['pid'],
        "type" => $type,
        "notify_url" => $notify_url,
        "return_url" => $return_url,
        "out_trade_no" => $out_trade_no,
        "name" => $name,
        "money" => $money,
        "clientip" => $ip,
        "device" => $device,
    );

    // 计算签名
    ksort($parameter);
    $signstr = '';
    foreach($parameter as $k => $v){
        if($k != "sign" && $k != "sign_type" && $v!=''){
            $signstr .= $k.'='.$v.'&';
        }
    }
    $signstr = substr($signstr,0,-1);
    $signstr .= $epay_config['key'];
    $sign = md5($signstr);

    $parameter['sign'] = $sign;
    $parameter['sign_type'] = 'MD5';

    // 调用API接口
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $epay_config['apiurl'].'mapi.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($parameter));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $response = curl_exec($ch);
    curl_close($ch);

    $result = json_decode($response, true);

    if ($result && $result['code'] == 1) {
        // 根据返回结果处理支付
        if (isset($result['payurl'])) {
            // 直接跳转支付
            $payUrl = $result['payurl'];
        } elseif (isset($result['qrcode'])) {
            // 生成二维码支付页面
            $payUrl = generateQRCodePage($result['qrcode'], $out_trade_no, $money, $name);
        } elseif (isset($result['urlscheme'])) {
            // 小程序跳转
            $payUrl = $result['urlscheme'];
        } else {
            exit('支付接口返回异常');
        }

        // 订单信息
        $orderInfo = array(
            'out_trade_no' => $out_trade_no,
            'money' => $money,
            'name' => $name,
            'type' => $type,
            'trade_no' => $result['trade_no'] ?? ''
        );

        // 使用移动端支付处理器生成页面
        echo generateMobilePayPage($payUrl, $type, $orderInfo);

    } else {
        exit('支付接口调用失败：' . ($result['msg'] ?? '未知错误'));
    }
}
