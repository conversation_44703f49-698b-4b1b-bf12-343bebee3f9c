<?php
/* *
 * 移动端支付跳转处理器
 * 功能：处理移动端支付URL scheme跳转问题
 * 解决：ERR_UNKNOWN_URL_SCHEME 错误
 */

/**
 * 检测用户设备类型
 * @return string 设备类型：mobile, pc
 */
function detectDevice() {
    $userAgent = strtolower($_SERVER['HTTP_USER_AGENT']);
    
    $mobileKeywords = array(
        'mobile', 'android', 'iphone', 'ipad', 'ipod', 
        'blackberry', 'windows phone', 'opera mini', 
        'iemobile', 'mobile safari'
    );
    
    foreach ($mobileKeywords as $keyword) {
        if (strpos($userAgent, $keyword) !== false) {
            return 'mobile';
        }
    }
    
    return 'pc';
}

/**
 * 检测是否在微信或QQ内置浏览器中
 * @return bool
 */
function isInAppBrowser() {
    $userAgent = strtolower($_SERVER['HTTP_USER_AGENT']);
    return (strpos($userAgent, 'micromessenger') !== false || 
            strpos($userAgent, 'qq/') !== false);
}

/**
 * 生成移动端友好的支付跳转页面
 * @param string $payUrl 支付URL
 * @param string $payType 支付类型 (alipay/wxpay)
 * @param array $orderInfo 订单信息
 * @return string HTML内容
 */
function generateMobilePayPage($payUrl, $payType, $orderInfo) {
    $device = detectDevice();
    $isInApp = isInAppBrowser();
    
    // 如果是在微信或QQ内置浏览器中，显示提示页面
    if ($isInApp) {
        return generateInAppTipPage($payUrl, $payType, $orderInfo);
    }
    
    // 如果是移动端，生成移动端优化的跳转页面
    if ($device === 'mobile') {
        return generateMobileOptimizedPage($payUrl, $payType, $orderInfo);
    }
    
    // PC端正常跳转
    return generatePCPayPage($payUrl, $payType, $orderInfo);
}

/**
 * 生成内置浏览器提示页面
 */
function generateInAppTipPage($payUrl, $payType, $orderInfo) {
    $payTypeName = ($payType === 'alipay') ? '支付宝' : '微信';
    
    return '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>请在浏览器中打开</title>
    <style>
        body { margin: 0; padding: 20px; background: #f5f5f5; font-family: Arial, sans-serif; }
        .container { max-width: 400px; margin: 50px auto; background: white; border-radius: 10px; padding: 30px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .icon { font-size: 48px; color: #ff6b6b; margin-bottom: 20px; }
        .title { font-size: 20px; color: #333; margin-bottom: 15px; }
        .message { color: #666; line-height: 1.6; margin-bottom: 25px; }
        .steps { text-align: left; background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .steps ol { margin: 0; padding-left: 20px; }
        .steps li { margin-bottom: 8px; color: #555; }
        .url-box { background: #e9ecef; padding: 10px; border-radius: 5px; word-break: break-all; font-size: 12px; color: #666; margin-bottom: 20px; }
        .btn { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">⚠️</div>
        <div class="title">无法在当前环境中打开' . $payTypeName . '</div>
        <div class="message">
            检测到您正在微信或QQ内置浏览器中访问，为了正常完成支付，请按以下步骤操作：
        </div>
        <div class="steps">
            <ol>
                <li>点击右上角的 <strong>···</strong> 菜单</li>
                <li>选择 <strong>"在浏览器中打开"</strong></li>
                <li>或复制下方链接到浏览器中打开</li>
            </ol>
        </div>
        <div class="url-box">' . htmlspecialchars($payUrl) . '</div>
        <a href="' . htmlspecialchars($payUrl) . '" class="btn">尝试打开支付页面</a>
        <a href="javascript:history.back()" class="btn" style="background: #6c757d;">返回上一页</a>
    </div>
</body>
</html>';
}

/**
 * 生成移动端优化的支付页面
 */
function generateMobileOptimizedPage($payUrl, $payType, $orderInfo) {
    $payTypeName = ($payType === 'alipay') ? '支付宝' : '微信';
    
    return '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正在跳转到' . $payTypeName . '支付</title>
    <style>
        body { margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); font-family: Arial, sans-serif; min-height: 100vh; }
        .container { max-width: 400px; margin: 50px auto; background: white; border-radius: 15px; padding: 30px; text-align: center; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #007bff; border-radius: 50%; width: 50px; height: 50px; animation: spin 1s linear infinite; margin: 0 auto 20px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .title { font-size: 18px; color: #333; margin-bottom: 15px; }
        .order-info { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: left; }
        .order-info div { margin-bottom: 8px; color: #555; }
        .order-info strong { color: #333; }
        .tips { font-size: 14px; color: #666; line-height: 1.5; margin-top: 20px; }
        .manual-btn { display: inline-block; padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 8px; margin-top: 15px; }
        .manual-btn:hover { background: #218838; }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <div class="title">正在跳转到' . $payTypeName . '支付...</div>
        <div class="order-info">
            <div><strong>订单号：</strong>' . htmlspecialchars($orderInfo['out_trade_no']) . '</div>
            <div><strong>支付金额：</strong>￥' . htmlspecialchars($orderInfo['money']) . '</div>
            <div><strong>商品名称：</strong>' . htmlspecialchars($orderInfo['name']) . '</div>
        </div>
        <div class="tips">
            如果页面没有自动跳转，请点击下方按钮手动打开支付页面
        </div>
        <a href="' . htmlspecialchars($payUrl) . '" class="manual-btn">手动打开支付页面</a>
    </div>
    
    <script>
        // 自动跳转逻辑
        setTimeout(function() {
            window.location.href = "' . addslashes($payUrl) . '";
        }, 2000);
        
        // 处理可能的URL scheme错误
        window.addEventListener("error", function(e) {
            if (e.message && e.message.indexOf("ERR_UNKNOWN_URL_SCHEME") !== -1) {
                alert("检测到支付跳转问题，请手动点击按钮打开支付页面");
            }
        });
    </script>
</body>
</html>';
}

/**
 * 生成PC端支付页面
 */
function generatePCPayPage($payUrl, $payType, $orderInfo) {
    return '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正在跳转到支付页面</title>
    <style>
        body { margin: 0; padding: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); font-family: Arial, sans-serif; }
        .container { position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%); text-align: center; color: white; }
        .loading { font-size: 18px; margin-bottom: 20px; }
        .spinner { border: 4px solid rgba(255,255,255,0.3); border-radius: 50%; border-top: 4px solid white; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto 20px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <div class="loading">正在为您跳转到支付页面，请稍候...</div>
        <p>订单号：' . htmlspecialchars($orderInfo['out_trade_no']) . '</p>
        <p>支付金额：￥' . htmlspecialchars($orderInfo['money']) . '</p>
    </div>
    
    <script>
        setTimeout(function() {
            window.location.href = "' . addslashes($payUrl) . '";
        }, 1000);
    </script>
</body>
</html>';
}
?>
