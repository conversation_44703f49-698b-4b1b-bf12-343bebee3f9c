<?php
require_once("mobile_pay_handler.php");

// 模拟测试数据
$testPayUrl = "https://sheng.nachengweb.com/submit.php?pid=1093&type=alipay&out_trade_no=TEST123456&name=测试商品&money=1.00&sign=abc123";
$testPayType = "alipay";
$testOrderInfo = array(
    'out_trade_no' => 'TEST123456',
    'money' => '1.00',
    'name' => '测试商品',
    'type' => 'alipay'
);

// 检测设备类型
$device = detectDevice();
$isInApp = isInAppBrowser();

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端支付测试页面</title>
    <style>
        body { margin: 0; padding: 20px; background: #f5f5f5; font-family: Arial, sans-serif; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .title { font-size: 24px; color: #333; margin-bottom: 20px; text-align: center; }
        .info-box { background: #e9ecef; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .info-box h4 { margin: 0 0 10px 0; color: #495057; }
        .info-box p { margin: 5px 0; color: #6c757d; }
        .test-section { margin-bottom: 30px; }
        .test-section h3 { color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
        .btn { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; text-align: center; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">移动端支付测试页面</h1>
        
        <div class="info-box">
            <h4>当前环境检测结果</h4>
            <p><strong>设备类型：</strong><?php echo $device; ?></p>
            <p><strong>是否在内置浏览器：</strong><?php echo $isInApp ? '是' : '否'; ?></p>
            <p><strong>User Agent：</strong><?php echo htmlspecialchars($_SERVER['HTTP_USER_AGENT']); ?></p>
        </div>
        
        <div class="test-section">
            <h3>文件完整性检查</h3>
            <?php
            $requiredFiles = array(
                'lib/epay_core.function.php',
                'lib/epay_md5.function.php', 
                'lib/epay_notify.class.php',
                'lib/epay_submit.class.php',
                'lib/EpayCore.class.php',
                'lib/pdoHelper.php',
                'mobile_pay_handler.php'
            );
            
            $allFilesExist = true;
            foreach ($requiredFiles as $file) {
                $exists = file_exists($file);
                if (!$exists) $allFilesExist = false;
                echo '<div class="status ' . ($exists ? 'success' : 'error') . '">';
                echo '<strong>' . $file . ':</strong> ' . ($exists ? '✓ 存在' : '✗ 缺失');
                echo '</div>';
            }
            
            if ($allFilesExist) {
                echo '<div class="status success"><strong>✓ 所有必需文件都已存在</strong></div>';
            } else {
                echo '<div class="status error"><strong>✗ 部分文件缺失，可能影响支付功能</strong></div>';
            }
            ?>
        </div>
        
        <div class="test-section">
            <h3>支付页面预览测试</h3>
            <p>点击下方按钮测试不同支付方式的页面显示效果：</p>
            
            <a href="?test=alipay" class="btn btn-success">测试支付宝支付页面</a>
            <a href="?test=wxpay" class="btn btn-warning">测试微信支付页面</a>
            <a href="?test=inapp" class="btn">测试内置浏览器提示页面</a>
        </div>
        
        <?php if (isset($_GET['test'])): ?>
        <div class="test-section">
            <h3>测试结果</h3>
            <?php
            switch ($_GET['test']) {
                case 'alipay':
                    echo '<p>正在生成支付宝支付页面...</p>';
                    echo '<iframe src="?preview=alipay" width="100%" height="400" style="border: 1px solid #ddd; border-radius: 5px;"></iframe>';
                    break;
                case 'wxpay':
                    echo '<p>正在生成微信支付页面...</p>';
                    echo '<iframe src="?preview=wxpay" width="100%" height="400" style="border: 1px solid #ddd; border-radius: 5px;"></iframe>';
                    break;
                case 'inapp':
                    echo '<p>正在生成内置浏览器提示页面...</p>';
                    echo '<iframe src="?preview=inapp" width="100%" height="400" style="border: 1px solid #ddd; border-radius: 5px;"></iframe>';
                    break;
            }
            ?>
        </div>
        <?php endif; ?>
        
        <div class="test-section">
            <h3>问题解决方案</h3>
            <div class="status success">
                <strong>✓ 已添加的文件：</strong>
                <ul>
                    <li>epay_core.function.php - 支付核心函数</li>
                    <li>epay_md5.function.php - MD5签名函数</li>
                    <li>epay_notify.class.php - 支付通知处理类</li>
                    <li>epay_submit.class.php - 支付提交类</li>
                    <li>mobile_pay_handler.php - 移动端支付处理器</li>
                </ul>
            </div>
            <div class="status success">
                <strong>✓ 移动端支付优化：</strong>
                <ul>
                    <li>自动检测设备类型（移动端/PC端）</li>
                    <li>检测微信/QQ内置浏览器环境</li>
                    <li>提供友好的跳转提示页面</li>
                    <li>处理URL scheme错误</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>

<?php
// 预览模式
if (isset($_GET['preview'])) {
    switch ($_GET['preview']) {
        case 'alipay':
            echo generateMobilePayPage($testPayUrl, 'alipay', $testOrderInfo);
            break;
        case 'wxpay':
            echo generateMobilePayPage($testPayUrl, 'wxpay', $testOrderInfo);
            break;
        case 'inapp':
            // 模拟内置浏览器环境
            $_SERVER['HTTP_USER_AGENT'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.0';
            echo generateInAppTipPage($testPayUrl, 'alipay', $testOrderInfo);
            break;
    }
    exit;
}
?>
